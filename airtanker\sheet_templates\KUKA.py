import re
from nameparser import <PERSON><PERSON><PERSON>
from services.DatabaseService import DatabaseService
import textwrap
from datetime import datetime, timedelta
from services.DateParser import get_date_exception, is_date_string
from services.ContractorService import get_name_error, get_standard_error, get_travel_error
from fuzzywuzzy import process
import os
from dotenv import load_dotenv
from main import airtanker_app

def parse_kuka_customer_sheet(sheet,
                            sheet_name,
                            file_name,
                            file_id,
                            error_entry_id_counter,
                            selected_week_ending,
                            database_service,
                            all_active_work_order_entries,
                            type):
    errors = []
    name_errors = []


    standard_rate_idx = 7
    overtime_rate_idx = 8
    doubletime_rate_idx = 9
    travel_rate_idx = 10

    date_idx = 3
    name_idx = 5
    standard_hours_idx = 11
    overtime_hours_idx = 12
    doubletime_hours_idx = 13
    travel_hours_idx = 14
    min_row = 2

    if type == "new":    
        # TODO: Modify these column indexes
        date_idx = 0
        name_idx = 1
        standard_hours_idx = 6
        overtime_hours_idx = 7
        doubletime_hours_idx = 8
        travel_hours_idx = 9
        min_row = 5

    employee_id = None

    all_active_work_order_entries = database_service.get_active_work_orders(selected_week_ending=selected_week_ending)
    
    # Extract just the names from all_employee_names for matching
    names_to_match_against = set([entry["FullName"] for entry in all_active_work_order_entries])


    # Parse the worksheet
    for row in sheet.iter_rows(min_row=min_row, values_only=True):  # Skip header row and blank part
        # Get the data        
        try:
            if can_be_int(row[name_idx]):
                name_idx = name_idx + 1

            if row[date_idx] == None or row[standard_hours_idx] == None or row[overtime_hours_idx] == None or row[doubletime_hours_idx] == None or row[travel_hours_idx] == None:
                break

            week_ending_date = row[date_idx]
            employee_name = HumanName(row[name_idx]) ## remove the () and everything in them. Hard code kind of, special circuumstance.

            # match work order name to name is spreadsheet
            best_match_name, score = process.extractOne(employee_name.full_name, names_to_match_against)
                
            # if name doesn't match the best
            if score < 90:
                no_name_found_error = True
                # Put into errors
                matches = process.extract(employee_name.full_name, names_to_match_against, limit=3)
                for match in matches:
                    match_name, score = match
                    # Get the hours and get the work orders of each
                    emps_work_orders = []
                    project_numbers = []
                    for wo_entry in all_active_work_order_entries:
                        if wo_entry["FullName"] == match_name:
                            emps_work_orders.append(wo_entry)
                            curr_project_number = wo_entry['ProjectNumber'].strip()
                            project_numbers.append(curr_project_number)

                    work_order_entry = {}
                    employee_id = emps_work_orders[0]        
                    for entry in emps_work_orders:
                        if entry['ProjectNumber'].strip() in work_order_entry:
                            # append it
                            work_order_entry[entry['ProjectNumber'].strip()].append({
                                entry['WorkOrderID']:entry['WorkOrderNumber'].strip()
                                })
                        else:
                            work_order_entry[entry["ProjectNumber"].strip()] = [{
                                entry['WorkOrderID']:entry['WorkOrderNumber'].strip()
                            }]

                    matching_entry = None
                    for error_entry in name_errors:
                        if error_entry['OriginalName'] == employee_name.full_name:
                            matching_entry = error_entry
                            break  # Stop searching once a match is found
                    if matching_entry:
                        data = {
                            'EmployeeName':match_name,
                            'EmployeeID':employee_id,
                            'WorkOrders':work_order_entry
                        }
                        matching_entry['EmployeeData'].append(data)
                    else:
                        name_error = {
                            'ID':error_entry_id_counter,
                            'OriginalName':employee_name.full_name,
                            'FileName': file_name,
                            'Message': f"Original Name: <strong>{employee_name.full_name}</strong>. No direct matches in the database. Please select correct employee.",
                            'EmployeeData':[{
                                'EmployeeName':match_name, 'EmployeeID':employee_id, 'WorkOrders':work_order_entry # It's the Project number and work order numbers
                            }],
                            'Hours':[]
                        }
                        name_errors.append(name_error)
                        error_entry_id_counter += 1
            else:
                no_name_found_error = False

            emps_work_orders = []
            project_numbers = []
            for wo_entry in all_active_work_order_entries:
                if wo_entry["FullName"] == best_match_name:
                    emps_work_orders.append(wo_entry)
                    curr_project_number = wo_entry['ProjectNumber'].strip()
                    project_numbers.append(curr_project_number)


            # get the hours
            standard_hours = row[standard_hours_idx]
            overtime_hours = row[overtime_hours_idx]
            doubletime_hours = row[doubletime_hours_idx]
            travel_hours = row[travel_hours_idx]


            if no_name_found_error:
                matching_entry = None
                for error_entry in name_errors:
                    if error_entry['OriginalName'] == employee_name.full_name:
                        matching_entry = error_entry
                        break  # Stop searching once a match is found
                if matching_entry:
                    if standard_hours != 0.00:
                        matching_entry['Hours'].append(
                            {'Date': week_ending_date, 'TaskID': "", 'Hours': standard_hours, 'FileID':file_id, 'RateTypeID': 2})
                    if overtime_hours != 0.00:
                        matching_entry['Hours'].append(
                            {'Date': week_ending_date, 'TaskID': "", 'Hours': overtime_hours, 'FileID':file_id, 'RateTypeID': 3})
                    if doubletime_hours != 0.00:
                        matching_entry['Hours'].append(
                            {'Date': week_ending_date, 'TaskID': "", 'Hours': doubletime_hours, 'FileID':file_id, 'RateTypeID': 4})
                    if travel_hours != 0.00:
                        matching_entry['Hours'].append(
                            {'Date': week_ending_date, 'TaskID': "", 'Hours': travel_hours, 'FileID':file_id, 'RateTypeID': 5})

            # if the found employee has more than one work order.
            # Add the employee and their work orders to the error list for user to select.
            elif len(emps_work_orders) > 1:
                # Put into errors.
                work_order_entry = {}
                for entry in emps_work_orders:
                    if entry['ProjectNumber'].strip() in work_order_entry:
                        # append it
                        work_order_entry[entry['ProjectNumber'].strip()].append({
                            entry['WorkOrderID']:entry['WorkOrderNumber'].strip()
                            })
                    else:
                        work_order_entry[entry["ProjectNumber"].strip()] = [{
                            entry['WorkOrderID']:entry['WorkOrderNumber'].strip()
                        }]

                # The WorkOrderEntry key contains all the work orders.
                # Next we'll need to add the hours.
                error = {
                    'ID':error_entry_id_counter,
                    'FileName': file_name,
                    'Message': f"More than one work order assigned.",
                    'WorkOrderEntry':work_order_entry,
                    'Employee': employee_name.full_name,                    
                    'EmployeeID':employee_id,
                    'Hours': [
                        # Add entries as needed
                    ]
                }

                if standard_hours != 0.00:
                    error['Hours'].append(
                        {'Date': week_ending_date, 'TaskID': "", 'Hours': standard_hours, 'FileID':file_id, 'RateTypeID': 2})
                if overtime_hours != 0.00:
                    error['Hours'].append(
                        {'Date': week_ending_date, 'TaskID': "", 'Hours': overtime_hours, 'FileID':file_id, 'RateTypeID': 3})
                if doubletime_hours != 0.00:
                    error['Hours'].append(
                        {'Date': week_ending_date, 'TaskID': "", 'Hours': doubletime_hours, 'FileID':file_id, 'RateTypeID': 4})
                if travel_hours != 0.00:
                    error['Hours'].append(
                        {'Date': week_ending_date, 'TaskID': "", 'Hours': travel_hours, 'FileID':file_id, 'RateTypeID': 5})
                  
                errors.append(error)
                # Increase the ID counter after each new entry into the error logs
                error_entry_id_counter += 1
                continue
            else:
                # Put the hours into DB
                employee_id = emps_work_orders[0]["EmployeeID"]
                project_id = emps_work_orders[0]['ProjectID']
                customer_id = emps_work_orders[0]['CustomerID']
                work_order_id = emps_work_orders[0]['WorkOrderID']
                if standard_hours != 0.00:
                    database_service.insert_customer_reported_hours_lastRow(employee_id=employee_id, 
                                                                date=week_ending_date, 
                                                                customer_reported_hours=standard_hours,
                                                                project_id=project_id,
                                                                customer_id=customer_id,
                                                                file_id=file_id,
                                                                work_order_id=work_order_id,
                                                                task_id=None,
                                                                location_id=None,
                                                                rate_type = 2)
                if overtime_hours != 0.00:
                    database_service.insert_customer_reported_hours_lastRow(employee_id=employee_id, 
                                                                date=week_ending_date, 
                                                                customer_reported_hours=overtime_hours,
                                                                project_id=project_id,
                                                                customer_id=customer_id,
                                                                file_id=file_id,
                                                                work_order_id=work_order_id,
                                                                task_id=None,
                                                                location_id=None,
                                                                rate_type = 3)
                if doubletime_hours != 0.00:
                    database_service.insert_customer_reported_hours_lastRow(employee_id=employee_id, 
                                                                date=week_ending_date, 
                                                                customer_reported_hours=doubletime_hours,
                                                                project_id=project_id,
                                                                customer_id=customer_id,
                                                                file_id=file_id,
                                                                work_order_id=work_order_id,
                                                                task_id=None,
                                                                location_id=None,
                                                                rate_type = 4)
                if travel_hours != 0.00:
                    database_service.insert_customer_reported_hours_lastRow(employee_id=employee_id, 
                                                                date=week_ending_date, 
                                                                customer_reported_hours=travel_hours,
                                                                project_id=project_id,
                                                                customer_id=customer_id,
                                                                file_id=file_id,
                                                                work_order_id=work_order_id,
                                                                task_id=None,
                                                                location_id=None,
                                                                rate_type = 5)
                                   
        except Exception as e:
            #print(e)
            continue
        
    return errors, name_errors, error_entry_id_counter


def can_be_int(value):
    try:
        int(value)
        return True
    except (ValueError, TypeError):
        return False
    

def parse_kuka(sheet,
               sheet_name,
               file_name,
               file_id,
               error_entry_id_counter,
               selected_week_ending,
               file,
               database_service, # type: DatabaseService
               all_active_work_order_entries):
    
    load_dotenv()

    errors = []
    name_errors = []

    data_offset = 10

    #####
    hours_col = "C"
    data_col = 3
    hours_row_start = 13
    row_offset = 10

    start_row = 13
    step = 10
    row_group_size = 3
    
    max_row = 75
    row = start_row
    column_index = 3  # Column "C" or index 3 (0-based index is 2)
    #####
    try:

        # Get static row data
        contractor_name = HumanName(sheet.cell(row=5, column=data_col).value)
        week_ending = sheet.cell(row=4, column=data_col).value
        job_num = sheet.cell(row=data_col, column=data_col).value
        if job_num is not None:
            job_num = re.sub(r'\([^)]*\)', '', job_num)
        else:
            job_num = ""

        task_id_travel = database_service.find_or_create_task("Travel")
        task_id_support = database_service.find_or_create_task("Support")

        # Extract just the names from all_employee_names for matching
        names_to_match_against = set([entry["FullName"] for entry in all_active_work_order_entries])

        best_match_name, score = process.extractOne(contractor_name.full_name, names_to_match_against)

        ## If the name doesn't match well
        no_name_found_error = False
        emps_work_orders = []
        project_numbers = []
        work_order_numbers = []
        if score < 90:
            name_matching_enabled = os.getenv('ENABLE_NAME_MATCHING', 'false')
            if name_matching_enabled == 'true':
                # Check the DB if name exists in view.
                query = """
                    SELECT TOP (1000) [FullName]
                        ,[EmpID]
                        ,[TS_Name]
                        ,[NameCount]
                    FROM [dbo].[View_NameSelectionCounts]
                    WHERE TS_NAME = ?
                    ORDER by NameCount
                """
                results = database_service.execute_query(query, contractor_name.full_name)
                if results:
                    best_match_name = results[0]["FullName"]
                    score = 100
            if score < 90:
                no_name_found_error, name_error = get_name_error(all_active_work_order_entries,
                                                                contractor_name,
                                                                names_to_match_against,
                                                                name_errors,
                                                                error_entry_id_counter,
                                                                file_name)
                if name_error:
                    name_errors.append(name_error)
                    error_entry_id_counter += 1

        if score > 89:
            for wo_entry in all_active_work_order_entries:
                if wo_entry["FullName"] == best_match_name:
                    emps_work_orders.append(wo_entry)
                    
                    curr_project_number = wo_entry['ProjectNumber'].strip()
                    project_numbers.append(curr_project_number)

                    curr_work_order_number = wo_entry["WorkOrderNumber"].strip()
                    work_order_numbers.append(curr_work_order_number)

            curr_employee_id = emps_work_orders[0]['EmployeeID']
        

        # Parse the worksheet.
        week_ending_subtract = 7
        project_number_mismatch = False
        #### Check correct project number, only if we found a single employee match. Multiple matches we skip this. 
        if not no_name_found_error:
            reported_project_number = job_num
            if reported_project_number:
                best_match_project_number, score = process.extractOne(str(reported_project_number), project_numbers) # Check which WO it is, if multiple.
                best_match_wo_number, wo_score = process.extractOne(str(reported_project_number), work_order_numbers) # Check which WO it is, if multiple.

                if score < 90 and wo_score < 90:
                    project_number_mismatch = True
            else:
                # There's no reported job number.
                project_number_mismatch = True
        while row <= max_row:
            daily_working_hours = 0 # reset to zero each day / every 10 row jumps
            daily_travel_hours = 0
            for r in range(row, row + row_group_size):
                if r > max_row:
                    break
                cell = sheet.cell(row=r, column=column_index)
                cell_value = cell.value
                                
                if (r - 13) % 10 == 0 and (r - 13) >= 0:
                    # This row is a working hours row
                    daily_working_hours += float(cell_value or 0)
                elif (r - 14) % 10 == 0 and (r - 14) >= 0:
                    # This row is a travel hours row
                    daily_travel_hours += float(cell_value or 0)
                elif (r - 15) % 10 == 0 and (r - 15) >= 0:
                    # This row is another working hours row
                    daily_working_hours += float(cell_value or 0)            
            try:
                week_ending_subtract -= 1
                curr_date = week_ending - timedelta(days=week_ending_subtract) # cell contains equation, doing it manually. 
                if daily_travel_hours > 0 or daily_working_hours > 0:
                    if project_number_mismatch:
                        # add travel horus
                        if  daily_travel_hours > 0:
                            error = get_standard_error(emps_work_orders,
                                            error_entry_id_counter,
                                            file_name,
                                            reported_project_number if isinstance(reported_project_number, str) else 0,
                                            contractor_name,
                                            curr_employee_id,
                                            curr_date,
                                            None,
                                            daily_travel_hours,
                                            file_id,
                                            errors,
                                            selected_week_ending,
                                            True)
                            if error:
                                errors.append(error)
                                error_entry_id_counter += 1
                        # add working hours
                        if  daily_working_hours > 0:
                            error = get_standard_error(emps_work_orders,
                                            error_entry_id_counter,
                                            file_name,
                                            reported_project_number if isinstance(reported_project_number, str) else 0,
                                            contractor_name,
                                            curr_employee_id,
                                            curr_date,
                                            task_id_support if isinstance(task_id_support, int) else None,
                                            daily_working_hours,
                                            file_id,
                                            errors,
                                            selected_week_ending,
                                            False)
                            if error:
                                errors.append(error)
                                error_entry_id_counter += 1

                    elif no_name_found_error:
                        matching_entry = None
                        for error_entry in name_errors:
                            if error_entry['OriginalName'] == contractor_name.full_name:
                                matching_entry = error_entry
                                break  # Stop searching once a match is found
                        if matching_entry:
                            if daily_travel_hours > 0:
                                data = {
                                    'Date':curr_date,
                                    'FileID':file_id,
                                    'Hours':daily_travel_hours,
                                    'TaskID':None,
                                    "UnknownTravel": True
                                }
                                matching_entry["Hours"].append(data)
                            
                            if daily_working_hours > 0:
                                data = {
                                    'Date':curr_date,
                                    'FileID':file_id,
                                    'Hours':daily_working_hours,
                                    'TaskID':task_id_support,
                                    "UnknownTravel": False
                                }
                                matching_entry["Hours"].append(data)
                            else:
                                database_service.delete_data_with_fileID(file_id)
                                raise Exception("There as a name mismatch but couldn't find the error in the name_errors array.")
                    else:

                        filtered_work_orders = [wo for wo in emps_work_orders if wo.get("ProjectNumber").strip() == best_match_project_number]
                        
                        if filtered_work_orders:
                            project_id = filtered_work_orders[0]["ProjectID"]
                            work_order_id = filtered_work_orders[0]["WorkOrderID"]
                            customer_id = filtered_work_orders[0]["CustomerID"]
                        else:
                            filtered_work_orders = [wo for wo in emps_work_orders if wo.get("WorkOrderNumber") == best_match_wo_number]
                            
                            project_id = filtered_work_orders[0]["ProjectID"]
                            work_order_id = filtered_work_orders[0]["WorkOrderID"]
                            customer_id = filtered_work_orders[0]["CustomerID"]

                        if daily_travel_hours > 0:
                            database_service.insert_hours_internal(employee_id=curr_employee_id,
                                                                date=curr_date,
                                                                employee_reported_hours=daily_travel_hours,
                                                                project_id=project_id,
                                                                customer_id=customer_id,
                                                                file_id=file_id,
                                                                work_order_id=work_order_id,
                                                                task_id = task_id_travel if isinstance(task_id_travel, int) else None,
                                                                location_id=None)
                            
                        if daily_working_hours > 0:
                            database_service.insert_hours_internal(employee_id=curr_employee_id,
                                                                date=curr_date,
                                                                employee_reported_hours=daily_working_hours,
                                                                project_id=project_id,
                                                                customer_id=customer_id,
                                                                file_id=file_id,
                                                                work_order_id=work_order_id,
                                                                task_id = task_id_support if isinstance(task_id_support, int) else None,
                                                                location_id=None)
                        
            except Exception as e:
                if "formulas" in e.args[0]:
                    airtanker_app.logger.debug(f"Error with {file_name}: {e}")
                    break
                continue

            row += step
        
    except Exception as e:
        print(f"Error in file at fileId {file_id} with error {e}")        

    return errors, name_errors, error_entry_id_counter
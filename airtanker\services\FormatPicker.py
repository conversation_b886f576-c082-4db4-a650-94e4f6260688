import pandas as pd
from enum import Enum, auto
from openpyxl.styles import <PERSON><PERSON><PERSON><PERSON>
from services import DatabaseService


class FormatType(Enum):
    KUKA = auto()
    ATS_CAMBRIDGE_SHORT = auto()
    ATS_CAMBRIDGE_LONG = auto()
    ATS_OHIO = auto()
    UNKNOWN = auto()


# Function to classify the format based on the number of columns
def determine_format(sheet,
                     sheet_name,
                     file_name,
                     error_entry_id_counter,
                     database_service # type: DatabaseService
                     ):
    format = None
    file_id = None
    error = None
    file_already_processed = None
    kuka_type = None
    try:

        # Get the number of non-empty columns
        non_empty_columns = sheet.cell(row=2, column=12).value
        # Determine the format
        if sheet.cell(row=1, column=1).value == "Type":            
            file_id, file_already_processed = database_service.insert_filename_get_id(filename=file_name, sheet_name=sheet_name, source_type="KUKA")                            
            format = FormatType.KUKA
            kuka_type = "old"
        elif sheet.cell(row=1, column=1).value == "Period":            
            file_id, file_already_processed = database_service.insert_filename_get_id(filename=file_name, sheet_name=sheet_name, source_type="KUKA")                            
            format = FormatType.KUKA
            kuka_type = "new"
        elif check_cell_background(sheet, 2, 5) or check_cell_background(sheet, 3, 5):
            file_id, file_already_processed = database_service.insert_filename_get_id(filename=file_name, sheet_name=sheet_name, source_type="ATS")
            format = FormatType.ATS_CAMBRIDGE_LONG
        elif check_cell_background(sheet, 2, 2) or check_cell_background(sheet, 3, 2):
            file_id, file_already_processed = database_service.insert_filename_get_id(filename=file_name, sheet_name=sheet_name, source_type="ATS")
            format = FormatType.ATS_CAMBRIDGE_SHORT
        elif non_empty_columns:
            file_id, file_already_processed = database_service.insert_filename_get_id(filename=file_name, sheet_name=sheet_name, source_type="ATS")
            format = FormatType.ATS_OHIO
        else:
            format = FormatType.UNKNOWN            
            raise Exception(f"Couldn't find an ATS format for {file_name}.")
    except Exception as e:
        error = {
            'ID':error_entry_id_counter,
            'FileName': file_name,
            "Message":f"Couldn't determine sheet format for file. Is this a file from the customer?",
            'ReportedProjectNumber':'',
            'WorkOrderEntry':{
            },
            'Employee': '',
            'EmployeeID':'',
            'Hours': []
        }
    
    if file_already_processed:
        error = {
            'ID':error_entry_id_counter,
            'FileName': file_name,
            "Message":f"File has already been processed. Delete the sheet at '/edit-files' to re-parse.",
            'ReportedProjectNumber':'',
            'WorkOrderEntry':{
            },
            'Employee': '',
            'EmployeeID':'',
            'Hours': []
        }
    return format, error, file_id, kuka_type


# Paths to the files
file_paths = [
    'Cambridge Long.xlsx',
    'Cambridge Short.xlsx',
    '_AtomTech Report Out 230127 (ATS Ohio).xlsx'
]

def check_cell_background(sheet, row, column):
    cell = sheet.cell(row=row, column=column)
    fill = cell.fill
    if fill.fill_type == "solid":
        rgb = fill.fgColor.rgb
        return rgb
    return None

# Classify each file
# for file_path in file_paths:
#     format_type = determine_format(file_path)
#     print(f'The file "{file_path}" is in {format_type.name} format.')

